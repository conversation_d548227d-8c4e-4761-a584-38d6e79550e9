import { ProcessedDiseaseData } from '../contexts/DiseaseDataContext';

// Twilio configuration - get from environment variables
const TWILIO_CONFIG = {
  accountSid: process.env.NEXT_PUBLIC_TWILIO_ACCOUNT_SID,
  authToken: process.env.NEXT_PUBLIC_TWILIO_AUTH_TOKEN,
  fromNumber: process.env.NEXT_PUBLIC_TWILIO_FROM_NUMBER,
  toNumber: process.env.NEXT_PUBLIC_TWILIO_TO_NUMBER || '+************', // default to your number
};

export interface SMSPredictionRequest {
  processedData: ProcessedDiseaseData;
  uploadCount: number;
  municipalities: string[];
}

export interface SMSPredictionResponse {
  success: boolean;
  messageSid?: string;
  error?: string;
  timestamp: string;
}

/**
 * Generate AI prediction message for SMS
 * Mas short pero informative para sa SMS format
 */
function generatePredictionMessage(processedData: ProcessedDiseaseData, uploadCount: number): string {
  // calculate total cases and top diseases
  const totalCases = Object.values(processedData).reduce((sum, data) => sum + data.totalCases, 0);
  
  // get top 3 diseases by case count
  const topDiseases = Object.entries(processedData)
    .sort(([,a], [,b]) => b.totalCases - a.totalCases)
    .slice(0, 3)
    .map(([disease, data]) => `${disease}: ${data.totalCases}`)
    .join(', ');

  // calculate trend prediction (simplified logic)
  const avgCasesPerDisease = totalCases / Math.max(Object.keys(processedData).length, 1);
  let trendPrediction = '';
  let explanation = '';

  if (avgCasesPerDisease > 50) {
    trendPrediction = 'RISING';
    explanation = 'High case density suggests potential outbreak risk. Enhanced surveillance recommended.';
  } else if (avgCasesPerDisease > 20) {
    trendPrediction = 'STABLE';
    explanation = 'Moderate case levels. Continue monitoring seasonal patterns.';
  } else {
    trendPrediction = 'DECLINING';
    explanation = 'Lower case density indicates effective control measures.';
  }

  // create concise SMS message
  const message = `
🏥 HEALTHRADAR AI PREDICTION (Upload #${uploadCount})

📊 CURRENT STATUS:
Total Cases: ${totalCases}
Top Diseases: ${topDiseases}

🔮 NEXT MONTH FORECAST: ${trendPrediction}
${explanation}

📍 Coverage: Mandaue, Consolacion, Lilo-an
⏰ ${new Date().toLocaleDateString('en-PH')}

Stay vigilant! 🛡️
  `.trim();

  return message;
}

/**
 * Send SMS prediction using Twilio API
 * Mag send ug SMS sa specified number with AI prediction
 */
export async function sendSMSPrediction(
  processedData: ProcessedDiseaseData,
  uploadCount: number
): Promise<SMSPredictionResponse> {
  try {
    // validate Twilio configuration
    if (!TWILIO_CONFIG.accountSid || !TWILIO_CONFIG.authToken || !TWILIO_CONFIG.fromNumber) {
      throw new Error('Twilio configuration incomplete. Check environment variables.');
    }

    // generate prediction message
    const message = generatePredictionMessage(processedData, uploadCount);
    
    console.log('📱 Preparing to send SMS prediction...');
    console.log('Message preview:', message.substring(0, 100) + '...');

    // prepare Twilio API request
    const twilioUrl = `https://api.twilio.com/2010-04-01/Accounts/${TWILIO_CONFIG.accountSid}/Messages.json`;
    
    // create form data for Twilio API
    const formData = new URLSearchParams();
    formData.append('To', TWILIO_CONFIG.toNumber!);
    formData.append('From', TWILIO_CONFIG.fromNumber!);
    formData.append('Body', message);

    // send SMS via Twilio API
    const response = await fetch(twilioUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Basic ${btoa(`${TWILIO_CONFIG.accountSid}:${TWILIO_CONFIG.authToken}`)}`,
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`Twilio API error: ${response.status} - ${errorData}`);
    }

    const responseData = await response.json();
    
    console.log('✅ SMS sent successfully!');
    console.log('Message SID:', responseData.sid);

    return {
      success: true,
      messageSid: responseData.sid,
      timestamp: new Date().toISOString(),
    };

  } catch (error) {
    console.error('❌ Failed to send SMS prediction:', error);
    
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      timestamp: new Date().toISOString(),
    };
  }
}

/**
 * Test Twilio connection
 * Send test message para ma check if working ang setup
 */
export async function testTwilioConnection(): Promise<{ connected: boolean; error?: string }> {
  try {
    // create minimal test data
    const testData: ProcessedDiseaseData = {
      'Test Disease': {
        totalCases: 1,
        municipalities: { 'Test': 1 },
        color: '#000000'
      }
    };

    const result = await sendSMSPrediction(testData, 0);
    
    if (result.success) {
      return { connected: true };
    } else {
      return { connected: false, error: result.error };
    }
  } catch (error) {
    return { 
      connected: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

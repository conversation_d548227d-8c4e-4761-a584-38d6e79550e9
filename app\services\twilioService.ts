// Twilio SMS Service for AI Predictions
// Sends SMS notifications with disease analysis and predictions

import { ProcessedDiseaseData } from '../contexts/DiseaseDataContext';

export interface TwilioSMSResponse {
  success: boolean;
  messageSid?: string;
  error?: string;
}

export interface SMSPredictionResponse {
  success: boolean;
  messageSid?: string;
  error?: string;
}

// generate AI prediction message for SMS
function generateSMSPrediction(data: ProcessedDiseaseData, uploadCount: number): string {
  // calculate total cases
  const totalCases = Object.values(data).reduce((sum, disease) => sum + disease.totalCases, 0);
  
  // get top 3 diseases
  const sortedDiseases = Object.entries(data)
    .sort(([, a], [, b]) => b.totalCases - a.totalCases)
    .slice(0, 3);
  
  // determine trend (simplified logic)
  const avgCases = totalCases / Object.keys(data).length;
  let forecast = "STABLE";
  let explanation = "Current case levels are within normal range.";
  
  if (avgCases > 100) {
    forecast = "RISING";
    explanation = "High case density suggests potential outbreak risk. Enhanced surveillance recommended.";
  } else if (avgCases < 20) {
    forecast = "DECLINING";
    explanation = "Case numbers are decreasing. Continue monitoring for sustained improvement.";
  }
  
  // format top diseases
  const topDiseases = sortedDiseases
    .map(([disease, data]) => `${disease}: ${data.totalCases}`)
    .join(', ');
  
  // create concise SMS message (160 char limit consideration)
  const message = `🏥 HEALTHRADAR AI PREDICTION (Upload #${uploadCount})

📊 CURRENT STATUS:
Total Cases: ${totalCases}
Top Diseases: ${topDiseases}

🔮 NEXT MONTH FORECAST: ${forecast}
${explanation}

📍 Coverage: Mandaue, Consolacion, Lilo-an
⏰ ${new Date().toLocaleDateString('en-PH')}

Stay vigilant! 🛡️`;

  return message;
}

// send SMS using Twilio
export async function sendSMSPrediction(
  processedData: ProcessedDiseaseData,
  uploadCount: number
): Promise<SMSPredictionResponse> {
  try {
    const message = generateSMSPrediction(processedData, uploadCount);
    
    // get Twilio configuration from environment variables
    const accountSid = process.env.NEXT_PUBLIC_TWILIO_ACCOUNT_SID;
    const authToken = process.env.NEXT_PUBLIC_TWILIO_AUTH_TOKEN;
    const fromNumber = process.env.NEXT_PUBLIC_TWILIO_PHONE_NUMBER;
    const toNumber = process.env.NEXT_PUBLIC_SMS_TARGET_NUMBER || '+************';
    
    if (!accountSid || !authToken || !fromNumber) {
      throw new Error('Twilio configuration incomplete. Check environment variables.');
    }
    
    console.log('📱 Sending SMS via Twilio...');
    console.log('📞 From:', fromNumber, 'To:', toNumber);
    console.log('📝 Message preview:', message.substring(0, 100) + '...');
    
    // send SMS via Twilio API
    const response = await fetch(`https://api.twilio.com/2010-04-01/Accounts/${accountSid}/Messages.json`, {
      method: 'POST',
      headers: {
        'Authorization': `Basic ${btoa(`${accountSid}:${authToken}`)}`,
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        From: fromNumber,
        To: toNumber,
        Body: message,
      }),
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Twilio API error: ${errorData.message || response.statusText}`);
    }
    
    const result = await response.json();
    
    console.log('✅ SMS sent successfully via Twilio!');
    console.log('📱 Message SID:', result.sid);
    
    return {
      success: true,
      messageSid: result.sid,
    };
    
  } catch (error) {
    console.error('❌ Twilio SMS error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown SMS error',
    };
  }
}

// test Twilio connection
export async function testTwilioConnection(): Promise<{ connected: boolean; error?: string }> {
  try {
    // create minimal test data
    const testData: ProcessedDiseaseData = {
      'Test Disease': {
        totalCases: 1,
        municipalities: { 'Test': 1 },
        color: '#000000'
      }
    };
    
    const result = await sendSMSPrediction(testData, 0);
    
    return {
      connected: result.success,
      error: result.error
    };
    
  } catch (error) {
    return {
      connected: false,
      error: error instanceof Error ? error.message : 'Unknown connection error'
    };
  }
}

// send custom SMS message (for testing)
export async function sendCustomSMS(message: string, phoneNumber?: string): Promise<TwilioSMSResponse> {
  try {
    const accountSid = process.env.NEXT_PUBLIC_TWILIO_ACCOUNT_SID;
    const authToken = process.env.NEXT_PUBLIC_TWILIO_AUTH_TOKEN;
    const fromNumber = process.env.NEXT_PUBLIC_TWILIO_PHONE_NUMBER;
    const toNumber = phoneNumber || process.env.NEXT_PUBLIC_SMS_TARGET_NUMBER || '+************';
    
    if (!accountSid || !authToken || !fromNumber) {
      throw new Error('Twilio configuration incomplete');
    }
    
    const response = await fetch(`https://api.twilio.com/2010-04-01/Accounts/${accountSid}/Messages.json`, {
      method: 'POST',
      headers: {
        'Authorization': `Basic ${btoa(`${accountSid}:${authToken}`)}`,
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        From: fromNumber,
        To: toNumber,
        Body: message,
      }),
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Twilio API error: ${errorData.message || response.statusText}`);
    }
    
    const result = await response.json();
    
    return {
      success: true,
      messageSid: result.sid,
    };
    
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

// Android SMS Gateway Service
// Uses SMS Gateway API to send SMS through your Android phone

import { ProcessedDiseaseData } from '../contexts/DiseaseDataContext';

export interface AndroidSMSResponse {
  success: boolean;
  messageId?: string;
  error?: string;
}

export interface SMSPredictionResponse {
  success: boolean;
  messageId?: string;
  error?: string;
}

// generate AI prediction message for SMS
function generateSMSPrediction(data: ProcessedDiseaseData, uploadCount: number): string {
  // calculate total cases
  const totalCases = Object.values(data).reduce((sum, disease) => sum + disease.totalCases, 0);
  
  // get top 3 diseases
  const sortedDiseases = Object.entries(data)
    .sort(([, a], [, b]) => b.totalCases - a.totalCases)
    .slice(0, 3);
  
  // determine trend (simplified logic)
  const avgCases = totalCases / Object.keys(data).length;
  let forecast = "STABLE";
  let explanation = "Current case levels are within normal range.";
  
  if (avgCases > 100) {
    forecast = "RISING";
    explanation = "High case density suggests potential outbreak risk. Enhanced surveillance recommended.";
  } else if (avgCases < 20) {
    forecast = "DECLINING";
    explanation = "Case numbers are decreasing. Continue monitoring for sustained improvement.";
  }
  
  // format top diseases
  const topDiseases = sortedDiseases
    .map(([disease, data]) => `${disease}: ${data.totalCases}`)
    .join(', ');
  
  // create concise SMS message (160 char limit consideration)
  const message = `🏥 HEALTHRADAR AI PREDICTION (Upload #${uploadCount})

📊 CURRENT STATUS:
Total Cases: ${totalCases}
Top Diseases: ${topDiseases}

🔮 NEXT MONTH FORECAST: ${forecast}
${explanation}

📍 Coverage: Mandaue, Consolacion, Lilo-an
⏰ ${new Date().toLocaleDateString('en-PH')}

Stay vigilant! 🛡️`;

  return message;
}

// send SMS using Android SMS Gateway
export async function sendSMSPrediction(
  processedData: ProcessedDiseaseData,
  uploadCount: number
): Promise<SMSPredictionResponse> {
  try {
    const message = generateSMSPrediction(processedData, uploadCount);
    
    // get configuration from environment variables
    const gatewayUrl = process.env.NEXT_PUBLIC_SMS_GATEWAY_URL;
    const apiKey = process.env.NEXT_PUBLIC_SMS_GATEWAY_API_KEY;
    const phoneNumber = process.env.NEXT_PUBLIC_SMS_TARGET_NUMBER || '+639690446511';
    
    if (!gatewayUrl || !apiKey) {
      throw new Error('SMS Gateway configuration incomplete. Check environment variables.');
    }
    
    console.log('📱 Sending SMS via Android Gateway...');
    console.log('📞 Target:', phoneNumber);
    console.log('📝 Message preview:', message.substring(0, 100) + '...');
    
    // send SMS via Android SMS Gateway API
    const response = await fetch(gatewayUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        phone: phoneNumber,
        message: message,
        priority: 1, // high priority for health alerts
      }),
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`SMS Gateway API error: ${response.status} - ${errorText}`);
    }
    
    const result = await response.json();
    
    console.log('✅ SMS sent successfully via Android Gateway!');
    console.log('📱 Message ID:', result.id || result.messageId);
    
    return {
      success: true,
      messageId: result.id || result.messageId || 'unknown',
    };
    
  } catch (error) {
    console.error('❌ Android SMS Gateway error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown SMS error',
    };
  }
}

// test Android SMS Gateway connection
export async function testAndroidSMSConnection(): Promise<{ connected: boolean; error?: string }> {
  try {
    // create minimal test data
    const testData: ProcessedDiseaseData = {
      'Test Disease': {
        totalCases: 1,
        municipalities: { 'Test': 1 },
        color: '#000000'
      }
    };
    
    const result = await sendSMSPrediction(testData, 0);
    
    return {
      connected: result.success,
      error: result.error
    };
    
  } catch (error) {
    return {
      connected: false,
      error: error instanceof Error ? error.message : 'Unknown connection error'
    };
  }
}

// send custom SMS message (for testing)
export async function sendCustomSMS(message: string, phoneNumber?: string): Promise<AndroidSMSResponse> {
  try {
    const gatewayUrl = process.env.NEXT_PUBLIC_SMS_GATEWAY_URL;
    const apiKey = process.env.NEXT_PUBLIC_SMS_GATEWAY_API_KEY;
    const targetNumber = phoneNumber || process.env.NEXT_PUBLIC_SMS_TARGET_NUMBER || '+639690446511';
    
    if (!gatewayUrl || !apiKey) {
      throw new Error('SMS Gateway configuration incomplete');
    }
    
    const response = await fetch(gatewayUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        phone: targetNumber,
        message: message,
        priority: 1,
      }),
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API error: ${response.status} - ${errorText}`);
    }
    
    const result = await response.json();
    
    return {
      success: true,
      messageId: result.id || result.messageId || 'unknown',
    };
    
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

// SMSGate.app Android SMS Gateway Service for AI Predictions
// Sends SMS notifications with disease analysis and predictions via SMSGate.app

import { ProcessedDiseaseData } from '../contexts/DiseaseDataContext';

export interface SMSGateResponse {
  success: boolean;
  messageId?: string;
  error?: string;
}

export interface SMSPredictionResponse {
  success: boolean;
  messageId?: string;
  error?: string;
}

// generate AI prediction message for SMS
function generateSMSPrediction(data: ProcessedDiseaseData, uploadCount: number): string {
  // calculate total cases
  const totalCases = Object.values(data).reduce((sum, disease) => sum + disease.totalCases, 0);
  
  // get top 3 diseases
  const sortedDiseases = Object.entries(data)
    .sort(([, a], [, b]) => b.totalCases - a.totalCases)
    .slice(0, 3);
  
  // determine trend (simplified logic)
  const avgCases = totalCases / Object.keys(data).length;
  let forecast = "STABLE";
  let explanation = "Current case levels are within normal range.";
  
  if (avgCases > 100) {
    forecast = "RISING";
    explanation = "High case density suggests potential outbreak risk. Enhanced surveillance recommended.";
  } else if (avgCases < 20) {
    forecast = "DECLINING";
    explanation = "Case numbers are decreasing. Continue monitoring for sustained improvement.";
  }
  
  // format top diseases
  const topDiseases = sortedDiseases
    .map(([disease, data]) => `${disease}: ${data.totalCases}`)
    .join(', ');
  
  // create concise SMS message
  const message = `🏥 HEALTHRADAR AI PREDICTION (Upload #${uploadCount})

📊 CURRENT STATUS:
Total Cases: ${totalCases}
Top Diseases: ${topDiseases}

🔮 NEXT MONTH FORECAST: ${forecast}
${explanation}

📍 Coverage: Mandaue, Consolacion, Lilo-an
⏰ ${new Date().toLocaleDateString('en-PH')}

Stay vigilant! 🛡️`;

  return message;
}

// send SMS using SMSGate.app
export async function sendSMSPrediction(
  processedData: ProcessedDiseaseData,
  uploadCount: number
): Promise<SMSPredictionResponse> {
  try {
    const message = generateSMSPrediction(processedData, uploadCount);
    
    // get SMSGate configuration from environment variables
    const apiUrl = process.env.NEXT_PUBLIC_SMSGATE_API_URL;
    const apiKey = process.env.NEXT_PUBLIC_SMSGATE_API_KEY;
    const deviceId = process.env.NEXT_PUBLIC_SMSGATE_DEVICE_ID;
    const phoneNumber = process.env.NEXT_PUBLIC_SMS_TARGET_NUMBER || '+639690446511';
    
    if (!apiUrl || !apiKey || !deviceId) {
      throw new Error('SMSGate configuration incomplete. Check environment variables.');
    }
    
    console.log('📱 Sending SMS via SMSGate.app...');
    console.log('📞 To:', phoneNumber);
    console.log('📝 Message preview:', message.substring(0, 100) + '...');
    
    // send SMS via SMSGate.app API
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        device_id: deviceId,
        phone_number: phoneNumber,
        message: message,
      }),
    });
    
    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`SMSGate API error: ${response.status} - ${errorData}`);
    }
    
    const result = await response.json();
    
    console.log('✅ SMS sent successfully via SMSGate.app!');
    console.log('📱 Message ID:', result.id || result.message_id);
    
    return {
      success: true,
      messageId: result.id || result.message_id || 'unknown',
    };
    
  } catch (error) {
    console.error('❌ SMSGate SMS error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown SMS error',
    };
  }
}

// test SMSGate connection
export async function testSMSGateConnection(): Promise<{ connected: boolean; error?: string }> {
  try {
    const testMessage = `🧪 SMSGate Test Message

This is a test from your Health Radar Disease Management System.

✅ If you receive this, SMSGate.app is working correctly!

⏰ ${new Date().toLocaleString('en-PH')}`;
    
    const result = await sendCustomSMS(testMessage);
    
    return {
      connected: result.success,
      error: result.error
    };
    
  } catch (error) {
    return {
      connected: false,
      error: error instanceof Error ? error.message : 'Unknown connection error'
    };
  }
}

// send custom SMS message (for testing)
export async function sendCustomSMS(message: string, phoneNumber?: string): Promise<SMSGateResponse> {
  try {
    const apiUrl = process.env.NEXT_PUBLIC_SMSGATE_API_URL;
    const apiKey = process.env.NEXT_PUBLIC_SMSGATE_API_KEY;
    const deviceId = process.env.NEXT_PUBLIC_SMSGATE_DEVICE_ID;
    const targetNumber = phoneNumber || process.env.NEXT_PUBLIC_SMS_TARGET_NUMBER || '+639690446511';
    
    if (!apiUrl || !apiKey || !deviceId) {
      throw new Error('SMSGate configuration incomplete');
    }
    
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        device_id: deviceId,
        phone_number: targetNumber,
        message: message,
      }),
    });
    
    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`SMSGate API error: ${response.status} - ${errorData}`);
    }
    
    const result = await response.json();
    
    return {
      success: true,
      messageId: result.id || result.message_id || 'unknown',
    };
    
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

// get device status from SMSGate
export async function getSMSGateDeviceStatus(): Promise<{ online: boolean; battery?: number; error?: string }> {
  try {
    const apiUrl = process.env.NEXT_PUBLIC_SMSGATE_API_URL;
    const apiKey = process.env.NEXT_PUBLIC_SMSGATE_API_KEY;
    const deviceId = process.env.NEXT_PUBLIC_SMSGATE_DEVICE_ID;
    
    if (!apiUrl || !apiKey || !deviceId) {
      throw new Error('SMSGate configuration incomplete');
    }
    
    // construct device status URL
    const statusUrl = apiUrl.replace('/send', `/devices/${deviceId}/status`);
    
    const response = await fetch(statusUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      throw new Error(`Device status error: ${response.status}`);
    }
    
    const result = await response.json();
    
    return {
      online: result.online || result.status === 'online',
      battery: result.battery_level || result.battery,
    };
    
  } catch (error) {
    return {
      online: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

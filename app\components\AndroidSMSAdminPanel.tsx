"use client";

import { useState } from 'react';
import { Card, CardHeader, CardBody, Button, Textarea, Input } from '@heroui/react';
import { resetUploadTracking, getUploadStatistics } from '../services/uploadTrackingService';
import { sendSMSPrediction, testAndroidSMSConnection, sendCustomSMS } from '../services/androidSMSService';
import { useDiseaseData } from '../contexts/DiseaseDataContext';

export default function AndroidSMSAdminPanel() {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<string>('');
  const [customMessage, setCustomMessage] = useState('');
  const [customNumber, setCustomNumber] = useState('');
  const { processedData } = useDiseaseData();

  const handleResetTracking = async () => {
    try {
      setLoading(true);
      setResult('Resetting upload tracking...');
      
      await resetUploadTracking();
      
      setResult('✅ Upload tracking reset successfully! Global counter is now 0.');
    } catch (error) {
      setResult(`❌ Error resetting tracking: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const handleTestSMS = async () => {
    try {
      setLoading(true);
      setResult('Testing Android SMS Gateway connection...');
      
      const connectionResult = await testAndroidSMSConnection();
      
      if (connectionResult.connected) {
        setResult('✅ Android SMS Gateway test successful! Check your phone for test SMS.');
      } else {
        setResult(`❌ Android SMS Gateway connection failed: ${connectionResult.error}`);
      }
    } catch (error) {
      setResult(`❌ Error testing connection: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const handleForceSMS = async () => {
    try {
      setLoading(true);
      setResult('Sending AI prediction SMS via Android Gateway...');
      
      const stats = await getUploadStatistics();
      const smsResult = await sendSMSPrediction(processedData, stats.totalUploads);
      
      if (smsResult.success) {
        setResult(`✅ AI prediction SMS sent successfully via Android Gateway! Message ID: ${smsResult.messageId}`);
      } else {
        setResult(`❌ Failed to send SMS: ${smsResult.error}`);
      }
    } catch (error) {
      setResult(`❌ Error sending SMS: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const handleCustomSMS = async () => {
    if (!customMessage.trim()) {
      setResult('❌ Please enter a message to send');
      return;
    }

    try {
      setLoading(true);
      setResult('Sending custom SMS...');
      
      const smsResult = await sendCustomSMS(customMessage, customNumber || undefined);
      
      if (smsResult.success) {
        setResult(`✅ Custom SMS sent successfully! Message ID: ${smsResult.messageId}`);
        setCustomMessage('');
        setCustomNumber('');
      } else {
        setResult(`❌ Failed to send custom SMS: ${smsResult.error}`);
      }
    } catch (error) {
      setResult(`❌ Error sending custom SMS: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const handleGetStats = async () => {
    try {
      setLoading(true);
      setResult('Fetching upload statistics...');
      
      const stats = await getUploadStatistics();
      
      const statsText = `
📊 UPLOAD STATISTICS:
• Total uploads: ${stats.totalUploads}
• Uploads until next SMS: ${stats.uploadsUntilNextSMS}
• Last SMS: ${stats.lastSMSDate ? new Date(stats.lastSMSDate).toLocaleString('en-PH') : 'Never'}

🏘️ MUNICIPALITY BREAKDOWN:
${Object.entries(stats.municipalityBreakdown)
  .map(([municipality, count]) => `• ${municipality}: ${count} uploads`)
  .join('\n') || '• No uploads yet'}
      `.trim();
      
      setResult(statsText);
    } catch (error) {
      setResult(`❌ Error fetching stats: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto shadow-lg border border-gray-100">
      <CardHeader className="bg-gradient-to-r from-green-600 to-green-700 text-white rounded-t-lg">
        <div className="flex flex-col">
          <h3 className="text-lg font-bold">🔧 Android SMS Admin Panel</h3>
          <p className="text-sm opacity-90">Manage Android SMS Gateway system</p>
        </div>
      </CardHeader>

      <CardBody className="p-6 space-y-4">
        {/* Action buttons */}
        <div className="grid grid-cols-2 gap-3">
          <Button
            color="primary"
            variant="flat"
            onPress={handleTestSMS}
            isLoading={loading}
            className="bg-blue-100 text-blue-700 hover:bg-blue-200"
          >
            📱 Test SMS Gateway
          </Button>
          
          <Button
            color="success"
            variant="flat"
            onPress={handleForceSMS}
            isLoading={loading}
            className="bg-green-100 text-green-700 hover:bg-green-200"
          >
            🚀 Force Send AI SMS
          </Button>
          
          <Button
            color="secondary"
            variant="flat"
            onPress={handleGetStats}
            isLoading={loading}
            className="bg-purple-100 text-purple-700 hover:bg-purple-200"
          >
            📊 Get Statistics
          </Button>
          
          <Button
            color="warning"
            variant="flat"
            onPress={handleResetTracking}
            isLoading={loading}
            className="bg-orange-100 text-orange-700 hover:bg-orange-200"
          >
            🔄 Reset Counter
          </Button>
        </div>

        {/* Custom SMS Section */}
        <div className="border-t pt-4">
          <h4 className="font-medium text-gray-800 mb-3">📝 Send Custom SMS:</h4>
          <div className="space-y-3">
            <Input
              label="Phone Number (optional)"
              placeholder="+639690446511"
              value={customNumber}
              onValueChange={setCustomNumber}
              variant="bordered"
              size="sm"
            />
            <Textarea
              label="Message"
              placeholder="Enter your custom SMS message..."
              value={customMessage}
              onValueChange={setCustomMessage}
              variant="bordered"
              minRows={3}
              maxRows={5}
            />
            <Button
              onPress={handleCustomSMS}
              isLoading={loading}
              className="bg-indigo-100 text-indigo-700 hover:bg-indigo-200"
              size="sm"
            >
              📤 Send Custom SMS
            </Button>
          </div>
        </div>

        {/* Result display */}
        {result && (
          <div className="mt-4">
            <label className="text-sm font-medium text-gray-700 mb-2 block">
              Result:
            </label>
            <Textarea
              value={result}
              readOnly
              minRows={3}
              maxRows={10}
              className="font-mono text-sm"
              variant="bordered"
            />
          </div>
        )}

        {/* Instructions */}
        <div className="bg-gray-50 rounded-lg p-4 mt-4">
          <h4 className="font-medium text-gray-800 mb-2">🛠️ Admin Functions:</h4>
          <ul className="text-sm text-gray-600 space-y-1">
            <li><strong>Test SMS Gateway:</strong> Send test message to verify Android connection</li>
            <li><strong>Force Send AI SMS:</strong> Manually trigger AI prediction SMS</li>
            <li><strong>Get Statistics:</strong> View current upload counts and SMS status</li>
            <li><strong>Reset Counter:</strong> Reset global upload counter to 0 (for testing)</li>
            <li><strong>Send Custom SMS:</strong> Send any custom message for testing</li>
          </ul>
        </div>

        {/* Environment check */}
        <div className="border-t pt-4">
          <h4 className="font-medium text-gray-800 mb-2">🔧 Configuration Status:</h4>
          <div className="space-y-1 text-sm">
            <div className="flex justify-between">
              <span>SMS Gateway URL:</span>
              <span className={process.env.NEXT_PUBLIC_SMS_GATEWAY_URL ? 'text-green-600' : 'text-red-600'}>
                {process.env.NEXT_PUBLIC_SMS_GATEWAY_URL ? '✅ Set' : '❌ Missing'}
              </span>
            </div>
            <div className="flex justify-between">
              <span>API Key:</span>
              <span className={process.env.NEXT_PUBLIC_SMS_GATEWAY_API_KEY ? 'text-green-600' : 'text-red-600'}>
                {process.env.NEXT_PUBLIC_SMS_GATEWAY_API_KEY ? '✅ Set' : '❌ Missing'}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Target Number:</span>
              <span className={process.env.NEXT_PUBLIC_SMS_TARGET_NUMBER ? 'text-green-600' : 'text-red-600'}>
                {process.env.NEXT_PUBLIC_SMS_TARGET_NUMBER || '❌ Missing'}
              </span>
            </div>
          </div>
        </div>
      </CardBody>
    </Card>
  );
}

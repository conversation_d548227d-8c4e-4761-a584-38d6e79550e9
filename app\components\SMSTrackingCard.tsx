"use client";

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Header, CardBody, Button } from '@heroui/react';
import { getUploadStatistics } from '../services/uploadTrackingService';
import { testTwilioConnection } from '../services/twilioService';

interface UploadStats {
  totalUploads: number;
  uploadsUntilNextSMS: number;
  lastSMSDate?: string;
  municipalityBreakdown: { [municipality: string]: number };
}

export default function SMSTrackingCard() {
  const [stats, setStats] = useState<UploadStats>({
    totalUploads: 0,
    uploadsUntilNextSMS: 4,
    municipalityBreakdown: {}
  });
  const [loading, setLoading] = useState(true);
  const [testingConnection, setTestingConnection] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<{
    tested: boolean;
    connected: boolean;
    error?: string;
  }>({ tested: false, connected: false });

  // fetch upload statistics on component mount
  useEffect(() => {
    fetchStats();
  }, []);

  const fetchStats = async () => {
    try {
      setLoading(true);
      const uploadStats = await getUploadStatistics();
      setStats(uploadStats);
    } catch (error) {
      console.error('Error fetching upload statistics:', error);
    } finally {
      setLoading(false);
    }
  };

  const testConnection = async () => {
    try {
      setTestingConnection(true);
      const result = await testTwilioConnection();
      setConnectionStatus({
        tested: true,
        connected: result.connected,
        error: result.error
      });
    } catch (error) {
      setConnectionStatus({
        tested: true,
        connected: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setTestingConnection(false);
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleDateString('en-PH', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getProgressPercentage = () => {
    const completed = 4 - stats.uploadsUntilNextSMS;
    return (completed / 4) * 100;
  };

  if (loading) {
    return (
      <Card className="h-full shadow-lg border border-gray-100">
        <CardBody className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#143D60]"></div>
        </CardBody>
      </Card>
    );
  }

  return (
    <Card className="h-full shadow-lg border border-gray-100">
      <CardHeader className="flex gap-3 items-center justify-between bg-gradient-to-r from-[#143D60] to-[#1e5a8a] text-white rounded-t-lg">
        <div className="flex flex-col">
          <h3 className="text-lg font-bold">📱 AI SMS Notifications</h3>
          <p className="text-sm opacity-90">Upload Tracking & Predictions</p>
        </div>
        <div className="text-right">
          <div className="text-2xl font-bold">{stats.totalUploads}</div>
          <div className="text-xs opacity-90">Total Uploads</div>
        </div>
      </CardHeader>

      <CardBody className="p-6 space-y-4">
        {/* Progress to next SMS */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium text-gray-700">Next SMS Trigger</span>
            <span className="text-sm text-gray-500">
              {stats.uploadsUntilNextSMS === 0 ? 'Ready!' : `${stats.uploadsUntilNextSMS} uploads left`}
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-gradient-to-r from-[#143D60] to-[#1e5a8a] h-2 rounded-full transition-all duration-300"
              style={{ width: `${getProgressPercentage()}%` }}
            ></div>
          </div>
          <p className="text-xs text-gray-500">
            SMS sent every 4th upload across all municipalities
          </p>
        </div>

        {/* Last SMS info */}
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium text-gray-700">Last SMS Sent</span>
            <span className="text-sm text-gray-600">{formatDate(stats.lastSMSDate)}</span>
          </div>
        </div>

        {/* Municipality breakdown */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700">Municipality Uploads</h4>
          <div className="space-y-1">
            {Object.entries(stats.municipalityBreakdown).map(([municipality, count]) => (
              <div key={municipality} className="flex justify-between items-center text-sm">
                <span className="text-gray-600">{municipality}</span>
                <span className="font-medium text-[#143D60]">{count}</span>
              </div>
            ))}
            {Object.keys(stats.municipalityBreakdown).length === 0 && (
              <p className="text-sm text-gray-500 italic">No uploads yet</p>
            )}
          </div>
        </div>

        {/* Twilio connection test */}
        <div className="border-t pt-4">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700">Twilio Status</span>
            {connectionStatus.tested && (
              <span className={`text-xs px-2 py-1 rounded-full ${
                connectionStatus.connected 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              }`}>
                {connectionStatus.connected ? '✅ Connected' : '❌ Failed'}
              </span>
            )}
          </div>
          
          <Button
            size="sm"
            variant="bordered"
            className="w-full"
            isLoading={testingConnection}
            onPress={testConnection}
          >
            {testingConnection ? 'Testing...' : 'Test SMS Connection'}
          </Button>
          
          {connectionStatus.tested && connectionStatus.error && (
            <p className="text-xs text-red-600 mt-1">{connectionStatus.error}</p>
          )}
        </div>

        {/* Refresh button */}
        <Button
          size="sm"
          variant="flat"
          className="w-full bg-[#143D60] text-white"
          onPress={fetchStats}
        >
          🔄 Refresh Stats
        </Button>
      </CardBody>
    </Card>
  );
}

# 📱 SMSGate.app Setup Guide

This guide will help you set up SMSGate.app integration to receive AI prediction notifications from the Health Radar Disease Management System using your Android phone.

## 🎯 Overview

The system sends AI-powered disease prediction SMS messages to your phone number (+************) after every 4th upload across all municipalities (Mandaue, Consolacion, Lilo-an) using your Android phone as the SMS gateway.

## 💰 Why SMSGate.app?

✅ **Completely FREE** - No monthly fees or per-SMS charges
✅ **Uses your phone** - Send SMS through your existing mobile plan
✅ **Simple setup** - Just install app and get API credentials
✅ **Reliable** - Professional SMS gateway service
✅ **Local control** - Your phone, your SMS, your data

## 📋 Prerequisites

- Android phone with SMS capability
- Phone number: +************ or ***********
- Stable internet connection (WiFi or mobile data)
- Computer/laptop running the Health Radar system

## 🚀 QUICK START (10 Minutes Setup)

### **Step 1: Install SMSGate.app**

1. **Go to**: https://smsgate.app/
2. **Click**: "Get Started" or "Download"
3. **Install the Android app** on your phone (+************)
4. **Create account** with your email
5. **Grant SMS permissions** when prompted

### **Step 2: Get API Credentials**

1. **Open SMSGate app** on your phone
2. **Go to**: Settings or API section
3. **Copy these values**:
   - **API URL**: Usually `https://api.smsgate.app/v1/send`
   - **API Key**: Your unique API key (looks like: `sg_xxxxxxxxxxxxxxxx`)
   - **Device ID**: Your device identifier (looks like: `device_xxxxxxxx`)

### **Step 3: Add to Environment Variables**

1. **Open your Health Radar project folder**
2. **Add to your `.env` file**:

```env
# SMSGate.app Configuration
NEXT_PUBLIC_SMSGATE_API_URL=https://api.smsgate.app/v1/send
NEXT_PUBLIC_SMSGATE_API_KEY=sg_xxxxxxxxxxxxxxxx
NEXT_PUBLIC_SMSGATE_DEVICE_ID=device_xxxxxxxx
NEXT_PUBLIC_SMS_TARGET_NUMBER=+************
```

### **Step 4: Test the Setup**

1. **Restart your Health Radar app**: `npm run dev`
2. **Go to**: Disease Management → "SMSGate Admin"
3. **Click**: "📱 Test SMSGate"
4. **Check your phone** for test SMS
5. **Should see**: ✅ "SMSGate connection test successful!"

### **Step 5: Verify AI Predictions**

1. **Upload CSV data** 4 times across any municipalities
2. **After 4th upload**: You should receive AI prediction SMS
3. **SMS includes**: Disease analysis, trends, and forecasts

**That's it! Your free SMS gateway is ready!** 🎉

## 📱 Detailed Setup Instructions

### **Installing SMSGate.app**

1. **Visit**: https://smsgate.app/
2. **Download**: Android app from Google Play Store or direct APK
3. **Install** on your phone (+************)
4. **Open app** and create account
5. **Verify** your phone number if required

### **Configuring the App**

1. **Grant permissions**:
   - SMS permissions (required)
   - Phone permissions (required)
   - Storage permissions (optional)

2. **Set up device**:
   - Give your device a name (e.g., "Health Radar SMS")
   - Enable SMS gateway service
   - Keep app running in background

3. **Get credentials**:
   - Note your API URL
   - Copy your API Key
   - Copy your Device ID

### **Network Requirements**

- **Internet connection**: WiFi or mobile data
- **Background running**: Keep SMSGate app running
- **Battery optimization**: Disable battery optimization for SMSGate app
- **Auto-start**: Enable auto-start for SMSGate app

## 🔧 Environment Variables Explained

```env
# SMSGate API endpoint for sending SMS
NEXT_PUBLIC_SMSGATE_API_URL=https://api.smsgate.app/v1/send

# Your unique API key from SMSGate app
NEXT_PUBLIC_SMSGATE_API_KEY=sg_xxxxxxxxxxxxxxxx

# Your device ID from SMSGate app
NEXT_PUBLIC_SMSGATE_DEVICE_ID=device_xxxxxxxx

# Your phone number (target for SMS)
NEXT_PUBLIC_SMS_TARGET_NUMBER=+************
```

## 📊 Features You Get

### **AI Prediction SMS Content**:
- 📊 Total disease cases across all municipalities
- 🏥 Top 3 diseases by case count
- 📈 Trend analysis (RISING/STABLE/DECLINING)
- 🔮 Next month forecast with explanation
- 📍 Coverage area (Mandaue, Consolacion, Lilo-an)
- ⏰ Timestamp and upload count

### **Admin Panel Features**:
- 📱 Test SMSGate connection
- 🚀 Force send AI prediction SMS
- 📊 View upload statistics
- 🔄 Reset upload counter
- 📡 Check device status and battery
- 📝 Send custom SMS messages

### **Dashboard Integration**:
- 📈 Upload progress to next SMS
- 🏘️ Municipality upload breakdown
- 🔋 Device status and battery level
- ⏰ Last SMS timestamp
- ✅ Configuration status indicators

## 🔧 Troubleshooting

### **Problem: "SMSGate configuration incomplete"**

**Solutions**:
1. **Check environment variables** in `.env` file
2. **Verify all 4 variables** are set correctly
3. **Restart your app** after adding variables
4. **Check for typos** in API credentials

### **Problem: "Device is offline"**

**Solutions**:
1. **Check internet connection** on your phone
2. **Open SMSGate app** and ensure it's running
3. **Restart SMSGate app** on your phone
4. **Check battery optimization** settings
5. **Ensure app has SMS permissions**

### **Problem: "SMS not received"**

**Solutions**:
1. **Check phone number format**: +************
2. **Verify SMS permissions** in SMSGate app
3. **Check mobile plan** has SMS capability
4. **Test with custom SMS** first
5. **Check device status** in admin panel

### **Problem: "API error"**

**Solutions**:
1. **Verify API credentials** are correct
2. **Check API URL** format
3. **Ensure device is registered** in SMSGate
4. **Check SMSGate service status**
5. **Try regenerating API key**

## 💡 Tips for Best Performance

### **Phone Setup**:
- **Keep SMSGate app running** in background
- **Disable battery optimization** for SMSGate
- **Enable auto-start** for SMSGate app
- **Maintain stable internet** connection
- **Keep phone charged** (check battery in admin panel)

### **Usage Optimization**:
- **Monitor device status** in dashboard
- **Test connection** regularly
- **Check upload progress** before expecting SMS
- **Use admin panel** for troubleshooting
- **Keep app updated** to latest version

## 📞 Support

### **If you encounter issues**:

1. **Check SMSGate Admin Panel** for configuration status
2. **Test connection** using "📱 Test SMSGate" button
3. **Verify device status** shows "🟢 Online"
4. **Check SMSGate app** is running on your phone
5. **Visit**: https://smsgate.app/support for app-specific help

### **Common Solutions**:
- **Restart SMSGate app** on your phone
- **Check internet connection** on both devices
- **Verify environment variables** are correct
- **Test with custom SMS** to isolate issues
- **Check phone SMS permissions**

## 🎉 Success Indicators

You'll know everything is working when:

- ✅ SMSGate configuration shows all green checkmarks
- ✅ Device status shows "🟢 Online" with battery level
- ✅ Test SMS is received on your phone
- ✅ AI prediction SMS arrives after every 4th upload
- ✅ SMS contains meaningful disease analysis and predictions

## 📋 Quick Reference

**Your Settings**:
- Phone: +************
- Target: AI predictions every 4 uploads
- Coverage: Mandaue, Consolacion, Lilo-an municipalities
- Gateway: SMSGate.app via your Android phone

**Environment Variables Template**:
```env
NEXT_PUBLIC_SMSGATE_API_URL=https://api.smsgate.app/v1/send
NEXT_PUBLIC_SMSGATE_API_KEY=sg_xxxxxxxxxxxxxxxx
NEXT_PUBLIC_SMSGATE_DEVICE_ID=device_xxxxxxxx
NEXT_PUBLIC_SMS_TARGET_NUMBER=+************
```

**SMSGate Website**: https://smsgate.app/

**Benefits**: 100% FREE, uses your phone, simple setup, reliable delivery! 📱✅🎉

"use client";

import { useState, useEffect } from 'react';
import { <PERSON>, CardHeader, CardBody, Button, Progress, Chip } from '@heroui/react';
import { getUploadStatistics } from '../services/uploadTrackingService';
import { testSMSGateConnection, getSMSGateDeviceStatus } from '../services/smsGateService';

interface UploadStats {
  totalUploads: number;
  uploadsUntilNextSMS: number;
  lastSMSDate: string | null;
  municipalityBreakdown: { [municipality: string]: number };
}

interface DeviceStatus {
  online: boolean;
  battery?: number;
  error?: string;
}

export default function SMSGateTrackingCard() {
  const [stats, setStats] = useState<UploadStats>({
    totalUploads: 0,
    uploadsUntilNextSMS: 4,
    lastSMSDate: null,
    municipalityBreakdown: {}
  });
  const [deviceStatus, setDeviceStatus] = useState<DeviceStatus>({ online: false });
  const [loading, setLoading] = useState(true);
  const [testing, setTesting] = useState(false);
  const [testResult, setTestResult] = useState<string>('');

  // fetch upload statistics
  const fetchStats = async () => {
    try {
      const uploadStats = await getUploadStatistics();
      setStats(uploadStats);
    } catch (error) {
      console.error('Error fetching upload stats:', error);
    }
  };

  // fetch device status
  const fetchDeviceStatus = async () => {
    try {
      const status = await getSMSGateDeviceStatus();
      setDeviceStatus(status);
    } catch (error) {
      console.error('Error fetching device status:', error);
      setDeviceStatus({ online: false, error: 'Connection failed' });
    }
  };

  // test SMSGate connection
  const handleTestConnection = async () => {
    setTesting(true);
    setTestResult('');
    
    try {
      const result = await testSMSGateConnection();
      
      if (result.connected) {
        setTestResult('✅ SMSGate connection test successful! Check your phone.');
        // refresh device status after successful test
        await fetchDeviceStatus();
      } else {
        setTestResult(`❌ Connection failed: ${result.error}`);
      }
    } catch (error) {
      setTestResult(`❌ Test error: ${error}`);
    } finally {
      setTesting(false);
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      await Promise.all([fetchStats(), fetchDeviceStatus()]);
      setLoading(false);
    };

    fetchData();
    
    // refresh stats every 30 seconds
    const interval = setInterval(fetchData, 30000);
    return () => clearInterval(interval);
  }, []);

  // calculate progress percentage
  const progressPercentage = stats.uploadsUntilNextSMS === 0 
    ? 100 
    : ((4 - stats.uploadsUntilNextSMS) / 4) * 100;

  return (
    <Card className="w-full shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300">
      <CardHeader className="bg-gradient-to-r from-green-600 to-blue-700 text-white rounded-t-lg">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center">
            <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
            </svg>
          </div>
          <div>
            <h3 className="text-lg font-bold">📱 AI SMS Notifications</h3>
            <p className="text-sm opacity-90">Powered by SMSGate.app</p>
          </div>
        </div>
      </CardHeader>

      <CardBody className="p-6">
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="w-6 h-6 border-2 border-green-600 border-t-transparent rounded-full animate-spin"></div>
            <span className="ml-3 text-gray-600">Loading SMS status...</span>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Device Status */}
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium text-gray-700">Device Status:</span>
                <Chip
                  color={deviceStatus.online ? "success" : "danger"}
                  variant="flat"
                  size="sm"
                >
                  {deviceStatus.online ? "🟢 Online" : "🔴 Offline"}
                </Chip>
              </div>
              {deviceStatus.battery && (
                <div className="text-sm text-gray-600">
                  🔋 {deviceStatus.battery}%
                </div>
              )}
            </div>

            {/* Upload Progress */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-gray-700">
                  Progress to Next SMS
                </span>
                <span className="text-sm text-gray-600">
                  {stats.uploadsUntilNextSMS === 0 ? 'Ready to send!' : `${stats.uploadsUntilNextSMS} uploads remaining`}
                </span>
              </div>
              <Progress
                value={progressPercentage}
                className="w-full"
                color={stats.uploadsUntilNextSMS === 0 ? "success" : "primary"}
                size="md"
              />
              <div className="text-xs text-gray-500 mt-1">
                Total uploads: {stats.totalUploads} • Next SMS at upload #{stats.totalUploads + stats.uploadsUntilNextSMS}
              </div>
            </div>

            {/* Municipality Breakdown */}
            {Object.keys(stats.municipalityBreakdown).length > 0 && (
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Municipality Uploads:</h4>
                <div className="grid grid-cols-3 gap-2">
                  {Object.entries(stats.municipalityBreakdown).map(([municipality, count]) => (
                    <div key={municipality} className="bg-gray-50 rounded-lg p-2 text-center">
                      <div className="text-xs text-gray-600">{municipality}</div>
                      <div className="text-lg font-bold text-green-600">{count}</div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Last SMS Info */}
            {stats.lastSMSDate && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                <div className="text-sm text-green-800">
                  <strong>Last SMS sent:</strong> {new Date(stats.lastSMSDate).toLocaleString('en-PH')}
                </div>
              </div>
            )}

            {/* Test Connection Button */}
            <div className="pt-2">
              <Button
                onPress={handleTestConnection}
                isLoading={testing}
                className="w-full bg-green-100 text-green-700 hover:bg-green-200 font-medium"
                size="sm"
              >
                {testing ? 'Testing Connection...' : '📱 Test SMSGate Connection'}
              </Button>
              
              {testResult && (
                <div className={`mt-2 p-2 rounded text-sm ${
                  testResult.includes('✅') 
                    ? 'bg-green-50 text-green-700 border border-green-200' 
                    : 'bg-red-50 text-red-700 border border-red-200'
                }`}>
                  {testResult}
                </div>
              )}
            </div>

            {/* Configuration Status */}
            <div className="border-t pt-3">
              <h4 className="text-xs font-medium text-gray-600 mb-2">SMSGate Configuration:</h4>
              <div className="space-y-1 text-xs">
                <div className="flex justify-between">
                  <span>API URL:</span>
                  <span className={process.env.NEXT_PUBLIC_SMSGATE_API_URL ? 'text-green-600' : 'text-red-600'}>
                    {process.env.NEXT_PUBLIC_SMSGATE_API_URL ? '✅ Set' : '❌ Missing'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>API Key:</span>
                  <span className={process.env.NEXT_PUBLIC_SMSGATE_API_KEY ? 'text-green-600' : 'text-red-600'}>
                    {process.env.NEXT_PUBLIC_SMSGATE_API_KEY ? '✅ Set' : '❌ Missing'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Device ID:</span>
                  <span className={process.env.NEXT_PUBLIC_SMSGATE_DEVICE_ID ? 'text-green-600' : 'text-red-600'}>
                    {process.env.NEXT_PUBLIC_SMSGATE_DEVICE_ID ? '✅ Set' : '❌ Missing'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Target Number:</span>
                  <span className={process.env.NEXT_PUBLIC_SMS_TARGET_NUMBER ? 'text-green-600' : 'text-red-600'}>
                    {process.env.NEXT_PUBLIC_SMS_TARGET_NUMBER || '❌ Missing'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}
      </CardBody>
    </Card>
  );
}

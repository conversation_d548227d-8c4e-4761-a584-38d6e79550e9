# 📱 Android SMS Gateway Setup Guide

This guide will help you set up your Android phone as an SMS gateway to receive AI prediction notifications from the Health Radar Disease Management System.

## 🎯 Overview

The system sends AI-powered disease prediction SMS messages to your phone number (+************) after every 4th upload across all municipalities (Mandaue, Consolacion, Lilo-an).

## 📋 Prerequisites

- Android phone with internet connection (WiFi or mobile data)
- Phone number: +************ or 09690446511
- Computer/laptop running the Health Radar system

## 🔧 Step 1: Install SMS Gateway App

### Option A: SMS Gateway API (Recommended)
1. **Download SMS Gateway API app** from Google Play Store:
   - Search for "SMS Gateway API" by SMS Gateway
   - Install the app on your Android phone

2. **Open the app** and complete initial setup:
   - Grant SMS permissions when prompted
   - Allow the app to send SMS messages
   - Enable "Auto-start" if prompted

### Option B: Alternative Apps
If SMS Gateway API is not available, you can use:
- **HTTP SMS Gateway**
- **SMS Gateway for Android**
- **SMS API Gateway**

## 🌐 Step 2: Configure Network Settings

### Enable Hotspot Method (Easiest):
1. **Enable Mobile Hotspot** on your Android phone:
   - Go to Settings > Network & Internet > Hotspot & Tethering
   - Turn on "Mobile Hotspot"
   - Note the hotspot name and password

2. **Connect your computer** to the phone's hotspot:
   - Connect your laptop/computer to the hotspot WiFi
   - This ensures both devices are on the same network

### WiFi Method (Alternative):
1. **Connect both devices** to the same WiFi network:
   - Connect your Android phone to your home/office WiFi
   - Connect your computer to the same WiFi network

## 📱 Step 3: Configure SMS Gateway App

1. **Open SMS Gateway API app** on your phone

2. **Start the Gateway Service**:
   - Tap "Start" or "Enable Gateway"
   - The app will show a local IP address (e.g., ************:9090)
   - **IMPORTANT**: Note this IP address and port

3. **Configure API Settings**:
   - Look for "API Key" or "Authentication" settings
   - Generate or note the API key (if required)
   - Some apps may not require an API key

4. **Test the Gateway**:
   - The app usually has a "Test" feature
   - Send a test SMS to verify it's working

## 🔑 Step 4: Update Environment Variables

1. **Open your Health Radar project folder**

2. **Create or edit `.env.local` file** in the root directory:

```env
# Android SMS Gateway Configuration
NEXT_PUBLIC_SMS_GATEWAY_URL=http://************:9090/api/send-sms
NEXT_PUBLIC_SMS_GATEWAY_API_KEY=your-api-key-here
NEXT_PUBLIC_SMS_TARGET_NUMBER=+************
```

3. **Replace the values**:
   - `************:9090` - Replace with your phone's IP and port
   - `your-api-key-here` - Replace with your API key (if required)
   - `+************` - Your phone number (already correct)

## 🚀 Step 5: Test the Setup

1. **Restart your Health Radar application**:
   ```bash
   npm run dev
   ```

2. **Go to Disease Management section**

3. **Click "Android SMS Admin" button**

4. **Click "📱 Test SMS Gateway"**:
   - This will send a test SMS to your phone
   - Check if you receive the test message

5. **If successful**, you should see:
   - ✅ "Android SMS Gateway test successful! Check your phone."
   - A test SMS on your phone

## 📊 Step 6: Verify AI Predictions

1. **Upload CSV data** 4 times across any municipalities

2. **After the 4th upload**, you should receive:
   - An AI-generated SMS with disease predictions
   - Analysis of current disease trends
   - Forecast for next month

3. **SMS will include**:
   - Total disease cases
   - Top diseases by municipality
   - Trend analysis (RISING/STABLE/DECLINING)
   - Recommendations

## 🔧 Troubleshooting

### Problem: "Connection failed" error

**Solutions**:
1. **Check network connection**:
   - Ensure both devices are on same network
   - Try the hotspot method if WiFi doesn't work

2. **Verify IP address**:
   - Check the IP shown in SMS Gateway app
   - Update the `.env.local` file with correct IP

3. **Check firewall**:
   - Some networks block local connections
   - Try using mobile hotspot instead

### Problem: "API Key error"

**Solutions**:
1. **Check if API key is required**:
   - Some apps don't need API keys
   - Try leaving it empty: `NEXT_PUBLIC_SMS_GATEWAY_API_KEY=`

2. **Generate new API key**:
   - Look for "Generate API Key" in the app
   - Copy the exact key to your `.env.local`

### Problem: SMS not received

**Solutions**:
1. **Check phone permissions**:
   - Ensure SMS permissions are granted
   - Check if app is running in background

2. **Verify phone number format**:
   - Try both `+************` and `09690446511`
   - Update in `.env.local` if needed

3. **Check SMS app settings**:
   - Ensure the gateway service is running
   - Restart the SMS Gateway app

## 📱 Alternative SMS Gateway Apps

If SMS Gateway API doesn't work, try these alternatives:

### 1. HTTP SMS Gateway
- Download from Play Store
- Similar setup process
- Usually uses port 8080

### 2. SMS Gateway for Android
- Free and open source
- Good documentation
- Reliable performance

### 3. Termux + SMS API
- For advanced users
- More customizable
- Requires terminal knowledge

## 🔄 Maintenance

### Daily:
- Keep SMS Gateway app running
- Ensure phone has internet connection

### Weekly:
- Check if IP address changed
- Update `.env.local` if needed
- Test SMS functionality

### Monthly:
- Update SMS Gateway app
- Clear app cache if issues occur

## 📞 Support

If you encounter issues:

1. **Check the Android SMS Admin Panel** in the Health Radar system
2. **View configuration status** (should show all ✅ green checkmarks)
3. **Try the test functions** to isolate the problem
4. **Check your phone's SMS Gateway app logs**

## 🎉 Success Indicators

You'll know everything is working when:

- ✅ Configuration status shows all green checkmarks
- ✅ Test SMS is received on your phone
- ✅ AI prediction SMS arrives after every 4th upload
- ✅ SMS contains meaningful disease analysis and predictions

## 📋 Quick Reference

**Your Settings**:
- Phone: +************
- SMS Gateway URL: `http://[YOUR-PHONE-IP]:9090/api/send-sms`
- Target: AI predictions every 4 uploads
- Coverage: Mandaue, Consolacion, Lilo-an municipalities

**Environment Variables**:
```env
NEXT_PUBLIC_SMS_GATEWAY_URL=http://************:9090/api/send-sms
NEXT_PUBLIC_SMS_GATEWAY_API_KEY=your-api-key
NEXT_PUBLIC_SMS_TARGET_NUMBER=+************
```

That's it! Your Android SMS Gateway is now ready to receive AI-powered health predictions! 🏥📱🤖

import { db } from '../../firebase';
import { doc, setDoc, getDoc } from 'firebase/firestore';
import { ProcessedDiseaseData } from '../contexts/DiseaseDataContext';
import { sendSMSPrediction } from './twilioService';

export interface UploadTrackingData {
  globalUploadCount: number;
  lastUploadTimestamp: string;
  lastSMSTimestamp?: string;
  municipalityUploads: {
    [municipality: string]: {
      uploadCount: number;
      lastUpload: string;
    };
  };
}

export interface SMSNotificationResult {
  triggered: boolean;
  success?: boolean;
  error?: string;
  uploadCount?: number;
  messageSid?: string;
}

/**
 * Get current global upload tracking data
 * Kuha ang current count sa tanan uploads across all municipalities
 */
export async function getUploadTrackingData(): Promise<UploadTrackingData> {
  try {
    const trackingRef = doc(db, 'healthradarDB', 'system', 'uploadTracking', 'global');
    const trackingDoc = await getDoc(trackingRef);
    
    if (trackingDoc.exists()) {
      return trackingDoc.data() as UploadTrackingData;
    } else {
      // initialize tracking data if wala pa
      const initialData: UploadTrackingData = {
        globalUploadCount: 0,
        lastUploadTimestamp: new Date().toISOString(),
        municipalityUploads: {}
      };
      
      await setDoc(trackingRef, initialData);
      return initialData;
    }
  } catch (error) {
    console.error('Error getting upload tracking data:', error);
    throw error;
  }
}

/**
 * Update global upload tracking after successful upload
 * Mag increment sa global counter and municipality-specific counter
 */
export async function updateUploadTracking(
  municipality: string,
  uploadedBy: string,
  uploadedByEmail: string
): Promise<UploadTrackingData> {
  try {
    const trackingRef = doc(db, 'healthradarDB', 'system', 'uploadTracking', 'global');
    const currentData = await getUploadTrackingData();
    
    // increment global upload count
    const newGlobalCount = currentData.globalUploadCount + 1;
    
    // update municipality-specific tracking
    if (!currentData.municipalityUploads[municipality]) {
      currentData.municipalityUploads[municipality] = {
        uploadCount: 0,
        lastUpload: ''
      };
    }
    
    currentData.municipalityUploads[municipality].uploadCount += 1;
    currentData.municipalityUploads[municipality].lastUpload = new Date().toISOString();
    
    // create updated tracking data
    const updatedData: UploadTrackingData = {
      ...currentData,
      globalUploadCount: newGlobalCount,
      lastUploadTimestamp: new Date().toISOString(),
    };
    
    // save updated tracking data
    await setDoc(trackingRef, updatedData);
    
    console.log(`📊 Upload tracking updated:`);
    console.log(`Global uploads: ${newGlobalCount}`);
    console.log(`${municipality} uploads: ${currentData.municipalityUploads[municipality].uploadCount}`);
    console.log(`Uploaded by: ${uploadedByEmail}`);
    
    return updatedData;
  } catch (error) {
    console.error('Error updating upload tracking:', error);
    throw error;
  }
}

/**
 * Check if SMS notification should be triggered
 * Mag check if naabot na ang 4th upload milestone
 */
export async function checkAndTriggerSMSNotification(
  processedData: ProcessedDiseaseData,
  trackingData: UploadTrackingData
): Promise<SMSNotificationResult> {
  try {
    const { globalUploadCount } = trackingData;
    
    // check if divisible by 4 (every 4th upload)
    if (globalUploadCount % 4 === 0 && globalUploadCount > 0) {
      console.log(`🚨 SMS TRIGGER: Reached ${globalUploadCount} uploads (4th upload milestone)`);
      
      // send SMS prediction
      const smsResult = await sendSMSPrediction(processedData, globalUploadCount);
      
      if (smsResult.success) {
        // update tracking data with SMS timestamp
        const trackingRef = doc(db, 'healthradarDB', 'system', 'uploadTracking', 'global');
        await setDoc(trackingRef, {
          ...trackingData,
          lastSMSTimestamp: new Date().toISOString()
        });
        
        console.log('✅ SMS notification sent successfully!');
        console.log('Message SID:', smsResult.messageSid);
        
        return {
          triggered: true,
          success: true,
          uploadCount: globalUploadCount,
          messageSid: smsResult.messageSid
        };
      } else {
        console.error('❌ SMS notification failed:', smsResult.error);
        
        return {
          triggered: true,
          success: false,
          error: smsResult.error,
          uploadCount: globalUploadCount
        };
      }
    } else {
      const remainingUploads = 4 - (globalUploadCount % 4);
      console.log(`📈 Upload count: ${globalUploadCount} (${remainingUploads} more uploads until next SMS)`);
      
      return {
        triggered: false,
        uploadCount: globalUploadCount
      };
    }
  } catch (error) {
    console.error('Error checking SMS notification trigger:', error);
    
    return {
      triggered: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Get upload statistics for dashboard display
 * Para ma display sa dashboard ang current upload stats
 */
export async function getUploadStatistics(): Promise<{
  totalUploads: number;
  uploadsUntilNextSMS: number;
  lastSMSDate?: string;
  municipalityBreakdown: { [municipality: string]: number };
}> {
  try {
    const trackingData = await getUploadTrackingData();
    const uploadsUntilNextSMS = 4 - (trackingData.globalUploadCount % 4);
    
    const municipalityBreakdown: { [municipality: string]: number } = {};
    Object.entries(trackingData.municipalityUploads).forEach(([municipality, data]) => {
      municipalityBreakdown[municipality] = data.uploadCount;
    });
    
    return {
      totalUploads: trackingData.globalUploadCount,
      uploadsUntilNextSMS: uploadsUntilNextSMS === 4 ? 0 : uploadsUntilNextSMS,
      lastSMSDate: trackingData.lastSMSTimestamp,
      municipalityBreakdown
    };
  } catch (error) {
    console.error('Error getting upload statistics:', error);
    return {
      totalUploads: 0,
      uploadsUntilNextSMS: 4,
      municipalityBreakdown: {}
    };
  }
}

/**
 * Reset upload tracking (for testing purposes)
 * Para ma reset ang counter if needed for testing
 */
export async function resetUploadTracking(): Promise<void> {
  try {
    const trackingRef = doc(db, 'healthradarDB', 'system', 'uploadTracking', 'global');
    const resetData: UploadTrackingData = {
      globalUploadCount: 0,
      lastUploadTimestamp: new Date().toISOString(),
      municipalityUploads: {}
    };
    
    await setDoc(trackingRef, resetData);
    console.log('🔄 Upload tracking reset successfully');
  } catch (error) {
    console.error('Error resetting upload tracking:', error);
    throw error;
  }
}

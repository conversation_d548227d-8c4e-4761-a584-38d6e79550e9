// Upload Tracking Service for SMSGate.app Android SMS Gateway
// Tracks uploads globally and triggers SMS notifications

import { doc, setDoc, getDoc } from 'firebase/firestore';
import { db } from '../service/service';
import { ProcessedDiseaseData } from '../contexts/DiseaseDataContext';
import { sendSMSPrediction } from './smsGateService';

export interface UploadTrackingData {
  globalUploadCount: number;
  lastUploadTimestamp: string;
  lastSMSTimestamp?: string;
  municipalityUploads: {
    [municipality: string]: {
      uploadCount: number;
      lastUpload: string;
    };
  };
}

export interface SMSNotificationResult {
  triggered: boolean;
  success?: boolean;
  error?: string;
  uploadCount: number;
  messageId?: string;
}

// update global upload tracking
export async function updateUploadTracking(
  municipality: string,
  userId: string,
  userEmail: string
): Promise<UploadTrackingData> {
  try {
    const trackingRef = doc(db, 'healthradarDB', 'system', 'uploadTracking', 'global');
    
    // get current tracking data
    const trackingDoc = await getDoc(trackingRef);
    const currentData: UploadTrackingData = trackingDoc.exists() 
      ? trackingDoc.data() as UploadTrackingData
      : {
          globalUploadCount: 0,
          lastUploadTimestamp: '',
          municipalityUploads: {}
        };
    
    // increment global counter
    currentData.globalUploadCount += 1;
    currentData.lastUploadTimestamp = new Date().toISOString();
    
    // update municipality-specific tracking
    if (!currentData.municipalityUploads[municipality]) {
      currentData.municipalityUploads[municipality] = {
        uploadCount: 0,
        lastUpload: ''
      };
    }
    
    currentData.municipalityUploads[municipality].uploadCount += 1;
    currentData.municipalityUploads[municipality].lastUpload = new Date().toISOString();
    
    // save updated tracking data
    await setDoc(trackingRef, currentData);
    
    console.log(`📊 Upload tracking updated: Global count = ${currentData.globalUploadCount}, ${municipality} count = ${currentData.municipalityUploads[municipality].uploadCount}`);
    
    return currentData;
    
  } catch (error) {
    console.error('Error updating upload tracking:', error);
    throw error;
  }
}

// check if SMS should be triggered and send if needed
export async function checkAndTriggerSMSNotification(
  processedData: ProcessedDiseaseData,
  trackingData: UploadTrackingData
): Promise<SMSNotificationResult> {
  try {
    const { globalUploadCount } = trackingData;
    
    // trigger SMS every 4th upload
    const shouldTriggerSMS = globalUploadCount % 4 === 0;
    
    if (!shouldTriggerSMS) {
      return {
        triggered: false,
        uploadCount: globalUploadCount
      };
    }
    
    console.log(`🚨 SMS trigger activated! Upload count: ${globalUploadCount}`);
    
    // send SMS prediction
    const smsResult = await sendSMSPrediction(processedData, globalUploadCount);
    
    if (smsResult.success) {
      // update last SMS timestamp
      const trackingRef = doc(db, 'healthradarDB', 'system', 'uploadTracking', 'global');
      await setDoc(trackingRef, {
        ...trackingData,
        lastSMSTimestamp: new Date().toISOString()
      });
      
      return {
        triggered: true,
        success: true,
        uploadCount: globalUploadCount,
        messageId: smsResult.messageId
      };
    } else {
      return {
        triggered: true,
        success: false,
        error: smsResult.error,
        uploadCount: globalUploadCount
      };
    }
    
  } catch (error) {
    console.error('Error in SMS notification check:', error);
    return {
      triggered: true,
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      uploadCount: trackingData.globalUploadCount
    };
  }
}

// get upload statistics for dashboard
export async function getUploadStatistics(): Promise<{
  totalUploads: number;
  uploadsUntilNextSMS: number;
  lastSMSDate: string | null;
  municipalityBreakdown: { [municipality: string]: number };
}> {
  try {
    const trackingRef = doc(db, 'healthradarDB', 'system', 'uploadTracking', 'global');
    const trackingDoc = await getDoc(trackingRef);
    
    if (!trackingDoc.exists()) {
      return {
        totalUploads: 0,
        uploadsUntilNextSMS: 4,
        lastSMSDate: null,
        municipalityBreakdown: {}
      };
    }
    
    const data = trackingDoc.data() as UploadTrackingData;
    const uploadsUntilNext = 4 - (data.globalUploadCount % 4);
    
    // extract municipality counts
    const municipalityBreakdown: { [municipality: string]: number } = {};
    Object.entries(data.municipalityUploads).forEach(([municipality, info]) => {
      municipalityBreakdown[municipality] = info.uploadCount;
    });
    
    return {
      totalUploads: data.globalUploadCount,
      uploadsUntilNextSMS: uploadsUntilNext === 4 ? 0 : uploadsUntilNext,
      lastSMSDate: data.lastSMSTimestamp || null,
      municipalityBreakdown
    };
    
  } catch (error) {
    console.error('Error fetching upload statistics:', error);
    throw error;
  }
}

// reset upload tracking (for testing)
export async function resetUploadTracking(): Promise<void> {
  try {
    const trackingRef = doc(db, 'healthradarDB', 'system', 'uploadTracking', 'global');
    
    const resetData: UploadTrackingData = {
      globalUploadCount: 0,
      lastUploadTimestamp: '',
      municipalityUploads: {}
    };
    
    await setDoc(trackingRef, resetData);
    console.log('🔄 Upload tracking reset successfully');
    
  } catch (error) {
    console.error('Error resetting upload tracking:', error);
    throw error;
  }
}

# 📱 Twilio SMS AI Prediction Setup Guide

## Overview
Your HealthRadar system now includes an AI-powered SMS notification system that automatically sends disease predictions via SMS after every 4th upload across all municipalities.

## 🚀 Features Implemented

### 1. **Global Upload Tracking**
- Tracks uploads across all municipalities (Mandaue, Consolacion, Lilo-an)
- Increments global counter for each successful upload
- Maintains municipality-specific upload counts

### 2. **AI SMS Predictions**
- Triggers automatically after every 4th upload
- Generates concise AI predictions for next month
- Includes current disease statistics and trend analysis
- Sends to your phone number: +************

### 3. **Dashboard Integration**
- New SMS Tracking Card on dashboard
- Shows upload progress to next SMS trigger
- Displays municipality upload breakdown
- Twilio connection testing functionality

## 🛠 Setup Instructions

### Step 1: Create Twilio Account

1. **Sign up for Twilio**
   - Go to [https://www.twilio.com/try-twilio](https://www.twilio.com/try-twilio)
   - Create a free account (you get $15 free credit)
   - Verify your phone number during signup

2. **Get Your Twilio Credentials**
   - After signup, go to Twilio Console Dashboard
   - Find your **Account SID** and **Auth Token**
   - Copy these values (you'll need them for environment variables)

### Step 2: Get a Twilio Phone Number

1. **Purchase a Phone Number**
   - In Twilio Console, go to "Phone Numbers" > "Manage" > "Buy a number"
   - Choose a number from your country (Philippines: +63) or US (+1)
   - SMS-capable numbers cost around $1/month
   - Complete the purchase

2. **Note Your Twilio Number**
   - Copy the purchased phone number (format: +**********)
   - This will be your "From" number for SMS

### Step 3: Configure Environment Variables

1. **Create `.env.local` file** in your project root:
```bash
# Copy from .env.example and fill in your values
cp .env.example .env.local
```

2. **Add your Twilio credentials** to `.env.local`:
```bash
# Twilio SMS Configuration
NEXT_PUBLIC_TWILIO_ACCOUNT_SID=ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
NEXT_PUBLIC_TWILIO_AUTH_TOKEN=your_auth_token_here
NEXT_PUBLIC_TWILIO_FROM_NUMBER=+**********
NEXT_PUBLIC_TWILIO_TO_NUMBER=+************
```

### Step 4: Test the Setup

1. **Start your development server**:
```bash
npm run dev
```

2. **Test Twilio connection**:
   - Go to Dashboard
   - Find the "📱 AI SMS Notifications" card
   - Click "Test SMS Connection" button
   - You should receive a test SMS

3. **Test AI predictions**:
   - Upload CSV data 4 times across any municipalities
   - After the 4th upload, you should receive an AI prediction SMS

## 📊 How It Works

### Upload Tracking Logic
```
Upload 1 (Mandaue) → Global count: 1 → No SMS
Upload 2 (Lilo-an) → Global count: 2 → No SMS  
Upload 3 (Consolacion) → Global count: 3 → No SMS
Upload 4 (Mandaue) → Global count: 4 → 🚨 SMS SENT!
Upload 5 (Lilo-an) → Global count: 5 → No SMS
...
Upload 8 (Any) → Global count: 8 → 🚨 SMS SENT!
```

### SMS Message Format
```
🏥 HEALTHRADAR AI PREDICTION (Upload #4)

📊 CURRENT STATUS:
Total Cases: 1,234
Top Diseases: Dengue: 456, Malaria: 234, Typhoid: 123

🔮 NEXT MONTH FORECAST: RISING
High case density suggests potential outbreak risk. Enhanced surveillance recommended.

📍 Coverage: Mandaue, Consolacion, Lilo-an
⏰ 1/28/2025

Stay vigilant! 🛡️
```

## 💰 Cost Estimation

### Twilio Pricing (Philippines)
- **Phone Number**: ~$1.00/month
- **SMS Messages**: ~$0.0075 per SMS (₱0.42)
- **Free Credit**: $15 (covers ~2,000 SMS messages)

### Monthly Cost Example
- 4 uploads per week = ~16 SMS per month
- Cost: 16 × $0.0075 = $0.12/month (₱6.72)
- Very affordable for the functionality provided!

## 🔧 Troubleshooting

### Common Issues

1. **"Twilio configuration incomplete" error**
   - Check that all environment variables are set correctly
   - Restart your development server after adding variables

2. **"Invalid phone number" error**
   - Ensure phone numbers include country code (+63 for Philippines)
   - Format: +************ (not ***********)

3. **"Insufficient funds" error**
   - Add credit to your Twilio account
   - Check your account balance in Twilio Console

4. **SMS not received**
   - Check if your phone number is verified in Twilio (for trial accounts)
   - Verify the "To" number format in environment variables
   - Check Twilio logs in Console for delivery status

### Testing Tips

1. **Use Twilio Trial Account**
   - Free $15 credit for testing
   - Can only send to verified phone numbers
   - Add your phone number to verified list

2. **Check Twilio Logs**
   - Go to Twilio Console > Monitor > Logs > Messaging
   - View delivery status and error details

3. **Test with Different Numbers**
   - Verify multiple phone numbers for testing
   - Test with both local and international numbers

## 🔒 Security Notes

1. **Environment Variables**
   - Never commit `.env.local` to version control
   - Keep your Auth Token secret
   - Rotate credentials periodically

2. **Phone Number Privacy**
   - Consider using a dedicated business number
   - Don't expose personal numbers in code

## 📞 Support

If you encounter issues:
1. Check Twilio Console logs first
2. Verify all environment variables
3. Test with Twilio's API Explorer
4. Contact Twilio support for account issues

## 🎉 Success!

Once setup is complete, your HealthRadar system will automatically:
- ✅ Track uploads across all municipalities
- ✅ Generate AI predictions every 4th upload
- ✅ Send SMS notifications to your phone
- ✅ Display tracking statistics on dashboard
- ✅ Provide connection testing tools

The system is now ready for production use! 🚀

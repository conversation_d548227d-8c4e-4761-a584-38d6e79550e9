# 📱 Twilio SMS Setup Guide

This guide will help you set up Twilio SMS integration to receive AI prediction notifications from the Health Radar Disease Management System.

## 🎯 Overview

The system sends AI-powered disease prediction SMS messages to your phone number (+************) after every 4th upload across all municipalities (Mandaue, Consolacion, Lilo-an).

## 💰 FREE TRIAL SETUP (Using Your $15 Credit)

**Good news!** You can use Twilio completely FREE with your $15 trial credit:

✅ **What's FREE:**
- $15 credit = ~380 SMS messages to Philippines
- 1 FREE US phone number during trial
- No credit card required for trial usage
- Perfect for months of testing

✅ **What you need:**
- Verify your phone number (+************)
- Get your free US phone number
- Copy your Account SID and Auth Token
- Add to your .env file

⚠️ **Trial limitations:**
- Can only send SMS to verified numbers (your +************)
- Messages will have "[Sent from your Twilio trial account]" prefix
- Must use US-based phone number (+1) as sender

## 🚀 QUICK START (5 Minutes Setup)

**Just want to get it working fast? Follow these steps:**

1. **Go to**: https://www.twilio.com/ → Sign up for free
2. **Verify your phone**: Enter +************ when asked
3. **Get free phone number**: Phone Numbers → Buy a number → Pick any US number
4. **Copy credentials**: Dashboard → Account SID & Auth Token
5. **Add to .env file**:
   ```env
   NEXT_PUBLIC_TWILIO_ACCOUNT_SID=ACxxxxxxxxxxxxx
   NEXT_PUBLIC_TWILIO_AUTH_TOKEN=your-token-here
   NEXT_PUBLIC_TWILIO_PHONE_NUMBER=+***********
   NEXT_PUBLIC_SMS_TARGET_NUMBER=+************
   ```
6. **Test**: Disease Management → SMS Admin → Test Twilio
7. **Done!** Upload 4 CSV files to get your first AI prediction SMS

**That's it! Your $15 credit will last for ~380 messages (months of usage).** 🎉

## 📋 Prerequisites

- Phone number: +************ or 0**********
- Credit card for Twilio account verification (free trial available)
- Computer/laptop running the Health Radar system

## 🔧 Step 1: Create Twilio Account

1. **Go to Twilio website**: https://www.twilio.com/
2. **Sign up for free account**:
   - Click "Sign up for free"
   - Enter your email and create password
   - Verify your email address

3. **Verify your phone number**:
   - Twilio will ask you to verify your phone number
   - Enter +************ (your target number)
   - Enter the verification code sent to your phone

## 💳 Step 2: Use Your $15 Free Credit

1. **You already have $15 free credit!**:
   - No credit card required for free trial
   - $15 credit = ~380 SMS messages to Philippines
   - Perfect for testing and initial usage

2. **Free trial limitations**:
   - Can only send SMS to **verified phone numbers**
   - Your number (+************) must be verified
   - All SMS will have "[Sent from your Twilio trial account]" prefix

## 📞 Step 3: Get FREE Twilio Phone Number

1. **Get your FREE trial phone number**:
   - In Twilio Console, go to **Phone Numbers > Manage > Buy a number**
   - **Filter by country**: United States (free trial includes 1 free US number)
   - **Select any number** with SMS capability
   - **Click "Buy"** - This uses your free trial, no charge!

2. **Your free trial includes**:
   - **1 FREE US phone number** (no monthly cost during trial)
   - Example: +***********
   - This will be your "From" number for SMS

## 🔑 Step 4: Get API Credentials

1. **Go to Twilio Console Dashboard**:
   - Find "Account Info" section
   - Copy your **Account SID** (starts with "AC...")
   - Copy your **Auth Token** (click to reveal)

2. **Keep these secure**:
   - Account SID: Public identifier
   - Auth Token: Secret key (keep private)

## 🔧 Step 5: Update Environment Variables

1. **Open your Health Radar project folder**

2. **Add to your `.env` file**:

```env
# Twilio SMS Configuration
NEXT_PUBLIC_TWILIO_ACCOUNT_SID=ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
NEXT_PUBLIC_TWILIO_AUTH_TOKEN=your-auth-token-here
NEXT_PUBLIC_TWILIO_PHONE_NUMBER=+***********
NEXT_PUBLIC_SMS_TARGET_NUMBER=+************
```

3. **Replace the values**:
   - `ACxxxxxxx...` - Your Account SID from Twilio Console
   - `your-auth-token-here` - Your Auth Token from Twilio Console
   - `+***********` - Your FREE trial Twilio phone number
   - `+************` - Your verified phone number (already correct)

## 🚀 Step 6: Test the Setup

1. **Restart your Health Radar application**:
   ```bash
   npm run dev
   ```

2. **Go to Disease Management section**

3. **Click "SMS Admin" button**

4. **Click "📱 Test Twilio"**:
   - This will send a test SMS to your phone
   - Check if you receive the test message

5. **If successful**, you should see:
   - ✅ "Twilio connection test successful! Check your phone."
   - A test SMS on your phone from your Twilio number

## 📊 Step 7: Verify AI Predictions

1. **Upload CSV data** 4 times across any municipalities

2. **After the 4th upload**, you should receive:
   - An AI-generated SMS with disease predictions
   - Analysis of current disease trends
   - Forecast for next month

3. **SMS will include**:
   - Total disease cases
   - Top diseases by municipality
   - Trend analysis (RISING/STABLE/DECLINING)
   - Recommendations

## 🔧 Troubleshooting

### Problem: "Twilio configuration incomplete"

**Solutions**:
1. **Check environment variables**:
   - Ensure all 4 Twilio variables are set in `.env`
   - Restart your application after adding variables

2. **Verify credentials**:
   - Double-check Account SID and Auth Token from Twilio Console
   - Make sure Auth Token is not expired

### Problem: "Twilio API error"

**Solutions**:
1. **Check phone number format**:
   - Twilio number: Must include country code (+1 for US)
   - Target number: +************ (Philippines format)

2. **Verify account status**:
   - Check if Twilio account is active
   - Ensure you have sufficient credit balance

3. **Check number capabilities**:
   - Ensure your Twilio number supports SMS
   - Verify the number is not suspended

### Problem: SMS not received

**Solutions**:
1. **Check phone number**:
   - Verify +************ is correct
   - Try alternative format: +63 **********

2. **Check message delivery**:
   - Go to Twilio Console > Monitor > Logs > Messages
   - Check delivery status of sent messages

3. **Network issues**:
   - SMS delivery to Philippines may take 1-5 minutes
   - Check if your phone has good signal

## 💰 Using Your $15 Free Credit

### What You Get FREE:
- **$15 credit** = ~380 SMS messages to Philippines
- **1 FREE US phone number** during trial
- **No credit card required** for trial usage
- **Perfect for testing** your Health Radar system

### Free Trial Limitations:
- **Verified numbers only**: Can only send to +************ (your verified number)
- **Trial message prefix**: "[Sent from your Twilio trial account - ]" added to messages
- **US number only**: Free number must be US-based (+1)

### Monitoring Your Credit:
- **Check balance** in Twilio Console dashboard
- **Track usage** in SMS tracking card
- **~380 messages** = plenty for months of testing
- **Each AI prediction** = 1 SMS = ~$0.04 from your credit

## 🔄 Maintenance

### Daily:
- Monitor SMS delivery in Twilio Console
- Check credit balance if needed

### Weekly:
- Review SMS logs for any delivery issues
- Test SMS functionality if problems occur

### Monthly:
- Review Twilio billing and usage
- Top up credit if running low

## 📞 Support

If you encounter issues:

1. **Check the SMS Admin Panel** in the Health Radar system
2. **View Twilio configuration status** (should show all ✅ green checkmarks)
3. **Check Twilio Console logs** for detailed error messages
4. **Contact Twilio support** for account-specific issues

## 🎉 Success Indicators

You'll know everything is working when:

- ✅ Twilio configuration shows all green checkmarks
- ✅ Test SMS is received on your phone
- ✅ AI prediction SMS arrives after every 4th upload
- ✅ SMS contains meaningful disease analysis and predictions

## 📋 Quick Reference

**Your Settings**:
- Phone: +************
- Target: AI predictions every 4 uploads
- Coverage: Mandaue, Consolacion, Lilo-an municipalities

**Environment Variables**:
```env
NEXT_PUBLIC_TWILIO_ACCOUNT_SID=ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
NEXT_PUBLIC_TWILIO_AUTH_TOKEN=your-auth-token-here
NEXT_PUBLIC_TWILIO_PHONE_NUMBER=+***********
NEXT_PUBLIC_SMS_TARGET_NUMBER=+************
```

**Twilio Console**: https://console.twilio.com/

That's it! Your Twilio SMS integration is now ready to send AI-powered health predictions! 🏥📱🤖

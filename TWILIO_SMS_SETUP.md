# 📱 Twilio SMS Setup Guide

This guide will help you set up Twilio SMS integration to receive AI prediction notifications from the Health Radar Disease Management System.

## 🎯 Overview

The system sends AI-powered disease prediction SMS messages to your phone number (+************) after every 4th upload across all municipalities (Mandaue, Consolacion, Lilo-an).

## 📋 Prerequisites

- Phone number: +************ or 0**********
- Credit card for Twilio account verification (free trial available)
- Computer/laptop running the Health Radar system

## 🔧 Step 1: Create Twilio Account

1. **Go to Twilio website**: https://www.twilio.com/
2. **Sign up for free account**:
   - Click "Sign up for free"
   - Enter your email and create password
   - Verify your email address

3. **Verify your phone number**:
   - <PERSON><PERSON><PERSON> will ask you to verify your phone number
   - Enter +************ (your target number)
   - Enter the verification code sent to your phone

## 💳 Step 2: Account Setup & Billing

1. **Complete account verification**:
   - <PERSON><PERSON><PERSON> may require identity verification
   - Add a credit card (required even for free trial)
   - **Note**: Free trial gives you $15 credit

2. **Understand pricing**:
   - SMS to Philippines: ~$0.0395 per message
   - With $15 free credit = ~380 free SMS messages
   - After free credit: Pay-as-you-go pricing

## 📞 Step 3: Get Twilio Phone Number

1. **Go to Phone Numbers** in Twilio Console:
   - Navigate to Phone Numbers > Manage > Buy a number
   - Choose country: **United States** (cheapest option)
   - Select any available number with SMS capability
   - Purchase the number (~$1/month)

2. **Note your Twilio phone number**:
   - Example: +**********
   - This will be your "From" number for SMS

## 🔑 Step 4: Get API Credentials

1. **Go to Twilio Console Dashboard**:
   - Find "Account Info" section
   - Copy your **Account SID** (starts with "AC...")
   - Copy your **Auth Token** (click to reveal)

2. **Keep these secure**:
   - Account SID: Public identifier
   - Auth Token: Secret key (keep private)

## 🔧 Step 5: Update Environment Variables

1. **Open your Health Radar project folder**

2. **Add to your `.env` file**:

```env
# Twilio SMS Configuration
NEXT_PUBLIC_TWILIO_ACCOUNT_SID=ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
NEXT_PUBLIC_TWILIO_AUTH_TOKEN=your-auth-token-here
NEXT_PUBLIC_TWILIO_PHONE_NUMBER=+**********
NEXT_PUBLIC_SMS_TARGET_NUMBER=+************
```

3. **Replace the values**:
   - `ACxxxxxxx...` - Your Account SID from Twilio Console
   - `your-auth-token-here` - Your Auth Token from Twilio Console
   - `+**********` - Your purchased Twilio phone number
   - `+************` - Your phone number (already correct)

## 🚀 Step 6: Test the Setup

1. **Restart your Health Radar application**:
   ```bash
   npm run dev
   ```

2. **Go to Disease Management section**

3. **Click "SMS Admin" button**

4. **Click "📱 Test Twilio"**:
   - This will send a test SMS to your phone
   - Check if you receive the test message

5. **If successful**, you should see:
   - ✅ "Twilio connection test successful! Check your phone."
   - A test SMS on your phone from your Twilio number

## 📊 Step 7: Verify AI Predictions

1. **Upload CSV data** 4 times across any municipalities

2. **After the 4th upload**, you should receive:
   - An AI-generated SMS with disease predictions
   - Analysis of current disease trends
   - Forecast for next month

3. **SMS will include**:
   - Total disease cases
   - Top diseases by municipality
   - Trend analysis (RISING/STABLE/DECLINING)
   - Recommendations

## 🔧 Troubleshooting

### Problem: "Twilio configuration incomplete"

**Solutions**:
1. **Check environment variables**:
   - Ensure all 4 Twilio variables are set in `.env`
   - Restart your application after adding variables

2. **Verify credentials**:
   - Double-check Account SID and Auth Token from Twilio Console
   - Make sure Auth Token is not expired

### Problem: "Twilio API error"

**Solutions**:
1. **Check phone number format**:
   - Twilio number: Must include country code (+1 for US)
   - Target number: +************ (Philippines format)

2. **Verify account status**:
   - Check if Twilio account is active
   - Ensure you have sufficient credit balance

3. **Check number capabilities**:
   - Ensure your Twilio number supports SMS
   - Verify the number is not suspended

### Problem: SMS not received

**Solutions**:
1. **Check phone number**:
   - Verify +************ is correct
   - Try alternative format: +63 **********

2. **Check message delivery**:
   - Go to Twilio Console > Monitor > Logs > Messages
   - Check delivery status of sent messages

3. **Network issues**:
   - SMS delivery to Philippines may take 1-5 minutes
   - Check if your phone has good signal

## 💰 Cost Management

### Free Trial Tips:
- **$15 free credit** = ~380 SMS messages
- **Monitor usage** in Twilio Console
- **Set up billing alerts** to avoid surprises

### After Free Trial:
- **Pay-as-you-go**: ~$0.04 per SMS to Philippines
- **Monthly phone number**: ~$1.00
- **Estimated monthly cost**: $2-5 for regular usage

### Cost Optimization:
- **Use sparingly**: AI SMS every 4th upload is already optimized
- **Monitor in dashboard**: Check SMS tracking card for usage
- **Set billing limits**: Configure in Twilio Console

## 🔄 Maintenance

### Daily:
- Monitor SMS delivery in Twilio Console
- Check credit balance if needed

### Weekly:
- Review SMS logs for any delivery issues
- Test SMS functionality if problems occur

### Monthly:
- Review Twilio billing and usage
- Top up credit if running low

## 📞 Support

If you encounter issues:

1. **Check the SMS Admin Panel** in the Health Radar system
2. **View Twilio configuration status** (should show all ✅ green checkmarks)
3. **Check Twilio Console logs** for detailed error messages
4. **Contact Twilio support** for account-specific issues

## 🎉 Success Indicators

You'll know everything is working when:

- ✅ Twilio configuration shows all green checkmarks
- ✅ Test SMS is received on your phone
- ✅ AI prediction SMS arrives after every 4th upload
- ✅ SMS contains meaningful disease analysis and predictions

## 📋 Quick Reference

**Your Settings**:
- Phone: +************
- Target: AI predictions every 4 uploads
- Coverage: Mandaue, Consolacion, Lilo-an municipalities

**Environment Variables**:
```env
NEXT_PUBLIC_TWILIO_ACCOUNT_SID=ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
NEXT_PUBLIC_TWILIO_AUTH_TOKEN=your-auth-token-here
NEXT_PUBLIC_TWILIO_PHONE_NUMBER=+**********
NEXT_PUBLIC_SMS_TARGET_NUMBER=+************
```

**Twilio Console**: https://console.twilio.com/

That's it! Your Twilio SMS integration is now ready to send AI-powered health predictions! 🏥📱🤖
